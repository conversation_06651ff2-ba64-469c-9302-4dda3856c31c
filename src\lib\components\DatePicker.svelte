<script lang="ts">
  export let selectedDate: Date | null = null;
  export let recurrenceType: 'none' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom' = 'none';
  export let recurrencePattern: any = null;
  export let placeholder = 'Select date';

  let showDatePicker = false;
  let showRecurrencePicker = false;

  // Helper function to format date
  function formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  // Generate next few dates for preview based on recurrence
  function getRecurrencePreview(): string[] {
    if (recurrenceType === 'none' || !selectedDate) return [];
    
    const previews: string[] = [];
    const baseDate = new Date(selectedDate);
    
    for (let i = 1; i <= 3; i++) {
      const nextDate = new Date(baseDate);
      
      switch (recurrenceType) {
        case 'daily':
          nextDate.setDate(baseDate.getDate() + i);
          break;
        case 'weekly':
          nextDate.setDate(baseDate.getDate() + (i * 7));
          break;
        case 'monthly':
          nextDate.setMonth(baseDate.getMonth() + i);
          break;
        case 'yearly':
          nextDate.setFullYear(baseDate.getFullYear() + i);
          break;
      }
      
      previews.push(formatDate(nextDate));
    }
    
    return previews;
  }

  // Handle quick date selections
  function selectQuickDate(days: number) {
    const date = new Date();
    date.setDate(date.getDate() + days);
    selectedDate = date;
    showDatePicker = false;
  }

  // Handle recurrence type change
  function handleRecurrenceChange(type: typeof recurrenceType) {
    recurrenceType = type;
    recurrencePattern = null;
    showRecurrencePicker = false;
  }
</script>

<div class="date-picker-container">
  <!-- Main date input -->
  <div class="date-input-group">
    <button 
      class="date-input" 
      class:active={showDatePicker}
      on:click={() => showDatePicker = !showDatePicker}
    >
      <svg class="calendar-icon" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
      </svg>
      <span class="date-text">
        {selectedDate ? formatDate(selectedDate) : placeholder}
      </span>
      <svg class="chevron-icon" class:rotated={showDatePicker} viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
      </svg>
    </button>

    <button 
      class="recurrence-button"
      class:active={showRecurrencePicker}
      on:click={() => showRecurrencePicker = !showRecurrencePicker}
    >
      <svg class="repeat-icon" viewBox="0 0 20 20" fill="currentColor">
        <path d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" />
      </svg>
      <span class="recurrence-text">
        {recurrenceType === 'none' ? 'Once' : recurrenceType.charAt(0).toUpperCase() + recurrenceType.slice(1)}
      </span>
    </button>
  </div>

  <!-- Date picker dropdown -->
  {#if showDatePicker}
    <div class="date-picker-dropdown">
      <div class="quick-dates">
        <button class="quick-date-btn" on:click={() => selectQuickDate(0)}>Today</button>
        <button class="quick-date-btn" on:click={() => selectQuickDate(1)}>Tomorrow</button>
        <button class="quick-date-btn" on:click={() => selectQuickDate(7)}>Next Week</button>
        <button class="quick-date-btn" on:click={() => selectQuickDate(30)}>Next Month</button>
      </div>
      
      <div class="date-input-native">
        <input 
          type="date" 
          bind:value={selectedDate}
          on:change={() => showDatePicker = false}
        />
      </div>
    </div>
  {/if}

  <!-- Recurrence picker dropdown -->
  {#if showRecurrencePicker}
    <div class="recurrence-picker-dropdown">
      <button class="recurrence-option" on:click={() => handleRecurrenceChange('none')}>
        <span>Once</span>
        <span class="recurrence-desc">No repetition</span>
      </button>
      <button class="recurrence-option" on:click={() => handleRecurrenceChange('daily')}>
        <span>Daily</span>
        <span class="recurrence-desc">Every day</span>
      </button>
      <button class="recurrence-option" on:click={() => handleRecurrenceChange('weekly')}>
        <span>Weekly</span>
        <span class="recurrence-desc">Every week</span>
      </button>
      <button class="recurrence-option" on:click={() => handleRecurrenceChange('monthly')}>
        <span>Monthly</span>
        <span class="recurrence-desc">Every month</span>
      </button>
      <button class="recurrence-option" on:click={() => handleRecurrenceChange('yearly')}>
        <span>Yearly</span>
        <span class="recurrence-desc">Every year</span>
      </button>
    </div>
  {/if}

  <!-- Recurrence preview -->
  {#if recurrenceType !== 'none' && selectedDate}
    <div class="recurrence-preview">
      <div class="preview-title">Next occurrences:</div>
      <div class="preview-dates">
        {#each getRecurrencePreview() as date}
          <span class="preview-date">{date}</span>
        {/each}
      </div>
    </div>
  {/if}
</div>

<style>
  .date-picker-container {
    position: relative;
    width: 100%;
  }

  .date-input-group {
    display: flex;
    gap: 0.5rem;
  }

  .date-input {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1rem;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    color: #2d3748;
  }

  .date-input:hover {
    border-color: rgba(66, 153, 225, 0.5);
    background: rgba(255, 255, 255, 0.95);
  }

  .date-input.active {
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .calendar-icon {
    width: 1.25rem;
    height: 1.25rem;
    color: #718096;
  }

  .date-text {
    flex: 1;
    text-align: left;
  }

  .chevron-icon {
    width: 1rem;
    height: 1rem;
    color: #718096;
    transition: transform 0.2s ease;
  }

  .chevron-icon.rotated {
    transform: rotate(180deg);
  }

  .recurrence-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem;
    background: rgba(245, 247, 250, 0.9);
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    color: #4a5568;
    white-space: nowrap;
  }

  .recurrence-button:hover {
    border-color: rgba(66, 153, 225, 0.5);
    background: rgba(66, 153, 225, 0.05);
  }

  .recurrence-button.active {
    border-color: #4299e1;
    background: rgba(66, 153, 225, 0.1);
  }

  .repeat-icon {
    width: 1rem;
    height: 1rem;
  }

  .date-picker-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: 0.5rem;
    background: white;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    padding: 1rem;
    z-index: 50;
    animation: slideDown 0.2s ease;
  }

  .recurrence-picker-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    background: white;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    padding: 0.5rem;
    z-index: 50;
    min-width: 200px;
    animation: slideDown 0.2s ease;
  }

  .quick-dates {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .quick-date-btn {
    padding: 0.5rem 0.75rem;
    background: rgba(66, 153, 225, 0.1);
    border: 1px solid rgba(66, 153, 225, 0.2);
    border-radius: 8px;
    color: #3182ce;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .quick-date-btn:hover {
    background: rgba(66, 153, 225, 0.2);
    border-color: rgba(66, 153, 225, 0.3);
  }

  .date-input-native input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 8px;
    font-size: 0.875rem;
    color: #2d3748;
  }

  .recurrence-option {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    padding: 0.75rem 1rem;
    background: transparent;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    text-align: left;
  }

  .recurrence-option:hover {
    background: rgba(66, 153, 225, 0.1);
  }

  .recurrence-option span:first-child {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 0.25rem;
  }

  .recurrence-desc {
    font-size: 0.75rem;
    color: #718096;
  }

  .recurrence-preview {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background: rgba(66, 153, 225, 0.05);
    border: 1px solid rgba(66, 153, 225, 0.1);
    border-radius: 8px;
  }

  .preview-title {
    font-size: 0.75rem;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 0.5rem;
  }

  .preview-dates {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .preview-date {
    padding: 0.25rem 0.5rem;
    background: rgba(66, 153, 225, 0.1);
    border-radius: 6px;
    font-size: 0.75rem;
    color: #3182ce;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @media (max-width: 768px) {
    .date-input-group {
      flex-direction: column;
      gap: 0.5rem;
    }

    .recurrence-button {
      justify-content: center;
    }

    .quick-dates {
      grid-template-columns: 1fr;
    }

    .recurrence-picker-dropdown {
      right: 0;
      left: 0;
    }
  }
</style>