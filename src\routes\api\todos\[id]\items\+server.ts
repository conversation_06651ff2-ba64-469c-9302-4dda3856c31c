import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getTodoItemsByListId, createTodoItem } from '$lib/server/db/operations.js';

export const GET: RequestHandler = async ({ params, locals }) => {
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const items = await getTodoItemsByListId(params.id);
    return json({ items });
  } catch (error) {
    console.error('Failed to fetch todo items:', error);
    return json({ error: 'Failed to fetch todo items' }, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ params, request, locals }) => {
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { title, description, orderIndex } = body;

    if (!title) {
      return json({ error: 'Title is required' }, { status: 400 });
    }

    const itemData = {
      todoListId: params.id,
      title,
      description,
      orderIndex: orderIndex || 0
    };

    const item = await createTodoItem(itemData);
    return json({ item }, { status: 201 });
  } catch (error) {
    console.error('Failed to create todo item:', error);
    return json({ error: 'Failed to create todo item' }, { status: 500 });
  }
};