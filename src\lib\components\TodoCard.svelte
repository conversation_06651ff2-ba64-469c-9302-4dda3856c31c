<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import DatePicker from './DatePicker.svelte';
  import TodoItem from './TodoItem.svelte';
  
  export let todo = {
    id: '',
    title: '',
    description: '',
    notes: '',
    priority: 'medium',
    isCompleted: false,
    dueDate: null,
    recurrenceType: 'none',
    recurrencePattern: null
  };
  export let items = [];
  export let isExpanded = false;
  export let isEditing = false;

  const dispatch = createEventDispatcher();
  
  let editTitle = todo.title;
  let editDescription = todo.description;
  let editNotes = todo.notes;
  let editPriority = todo.priority;
  let editDueDate = todo.dueDate;
  let editRecurrenceType = todo.recurrenceType;
  let editRecurrencePattern = todo.recurrencePattern;
  let newItemTitle = '';
  let showAddItem = false;

  const priorities = [
    { value: 'low', label: 'Low', color: '#3182ce' },
    { value: 'medium', label: 'Medium', color: '#319795' },
    { value: 'high', label: 'High', color: '#d69e2e' },
    { value: 'urgent', label: 'Urgent', color: '#c53030' }
  ];

  function getPriorityColor(priority: string) {
    return priorities.find(p => p.value === priority)?.color || '#319795';
  }

  function formatDate(date: string | Date | null) {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: d.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
    });
  }

  function isOverdue(date: string | Date | null) {
    if (!date || todo.isCompleted) return false;
    return new Date(date) < new Date();
  }

  function getCompletedItemsCount() {
    return items.filter(item => item.isCompleted).length;
  }

  function handleToggleComplete() {
    dispatch('toggle', {
      id: todo.id,
      isCompleted: !todo.isCompleted
    });
  }

  function handleSaveEdit() {
    if (editTitle.trim()) {
      dispatch('update', {
        id: todo.id,
        title: editTitle.trim(),
        description: editDescription.trim(),
        notes: editNotes.trim(),
        priority: editPriority,
        dueDate: editDueDate,
        recurrenceType: editRecurrenceType,
        recurrencePattern: editRecurrencePattern
      });
      isEditing = false;
    }
  }

  function handleCancelEdit() {
    editTitle = todo.title;
    editDescription = todo.description;
    editNotes = todo.notes;
    editPriority = todo.priority;
    editDueDate = todo.dueDate;
    editRecurrenceType = todo.recurrenceType;
    editRecurrencePattern = todo.recurrencePattern;
    isEditing = false;
  }

  function handleDelete() {
    dispatch('delete', { id: todo.id });
  }

  function handleAddItem() {
    if (newItemTitle.trim()) {
      dispatch('addItem', {
        todoId: todo.id,
        title: newItemTitle.trim()
      });
      newItemTitle = '';
      showAddItem = false;
    }
  }

  function handleItemUpdate(event) {
    dispatch('updateItem', {
      todoId: todo.id,
      ...event.detail
    });
  }

  function handleItemToggle(event) {
    dispatch('toggleItem', {
      todoId: todo.id,
      ...event.detail
    });
  }

  function handleItemDelete(event) {
    dispatch('deleteItem', {
      todoId: todo.id,
      ...event.detail
    });
  }
</script>

<div class="todo-card" class:completed={todo.isCompleted} class:overdue={isOverdue(todo.dueDate)} class:editing={isEditing}>
  <!-- Header -->
  <div class="todo-header">
    <div class="header-left">
      <button 
        class="checkbox"
        class:checked={todo.isCompleted}
        on:click={handleToggleComplete}
        disabled={isEditing}
      >
        {#if todo.isCompleted}
          <svg class="check-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        {/if}
      </button>

      <div class="todo-info">
        {#if isEditing}
          <input 
            class="edit-title-input"
            bind:value={editTitle}
            placeholder="Todo title..."
            autofocus
          />
        {:else}
          <h3 class="todo-title" class:struck={todo.isCompleted}>
            {todo.title}
          </h3>
        {/if}

        <div class="todo-meta">
          <span 
            class="priority-badge" 
            style="background-color: {getPriorityColor(todo.priority)}15; color: {getPriorityColor(todo.priority)}"
          >
            {priorities.find(p => p.value === todo.priority)?.label || 'Medium'}
          </span>

          {#if todo.dueDate}
            <span class="due-date" class:overdue={isOverdue(todo.dueDate)}>
              <svg class="calendar-icon" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
              </svg>
              {formatDate(todo.dueDate)}
            </span>
          {/if}

          {#if todo.recurrenceType !== 'none'}
            <span class="recurrence-badge">
              <svg class="repeat-icon" viewBox="0 0 20 20" fill="currentColor">
                <path d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" />
              </svg>
              {todo.recurrenceType}
            </span>
          {/if}

          {#if items.length > 0}
            <span class="items-count">
              {getCompletedItemsCount()}/{items.length} items
            </span>
          {/if}
        </div>
      </div>
    </div>

    <div class="header-actions">
      {#if isEditing}
        <button class="action-btn save-btn" on:click={handleSaveEdit}>
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </button>
        <button class="action-btn cancel-btn" on:click={handleCancelEdit}>
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      {:else}
        <button class="action-btn expand-btn" on:click={() => isExpanded = !isExpanded}>
          <svg class="expand-icon" class:rotated={isExpanded} viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
        <button class="action-btn edit-btn" on:click={() => isEditing = true}>
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
          </svg>
        </button>
        <button class="action-btn delete-btn" on:click={handleDelete}>
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd" />
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </button>
      {/if}
    </div>
  </div>

  <!-- Description -->
  {#if (todo.description || isEditing) && (isExpanded || isEditing)}
    <div class="todo-description">
      {#if isEditing}
        <textarea 
          class="edit-description-input"
          bind:value={editDescription}
          placeholder="Description (optional)..."
          rows="3"
        ></textarea>
      {:else if todo.description}
        <p class:struck={todo.isCompleted}>{todo.description}</p>
      {/if}
    </div>
  {/if}

  <!-- Date and recurrence settings in edit mode -->
  {#if isEditing}
    <div class="edit-settings">
      <div class="setting-group">
        <label class="setting-label">Priority</label>
        <select class="priority-select" bind:value={editPriority}>
          {#each priorities as priority}
            <option value={priority.value}>{priority.label}</option>
          {/each}
        </select>
      </div>

      <div class="setting-group">
        <label class="setting-label">Due Date & Recurrence</label>
        <DatePicker 
          bind:selectedDate={editDueDate}
          bind:recurrenceType={editRecurrenceType}
          bind:recurrencePattern={editRecurrencePattern}
        />
      </div>
    </div>
  {/if}

  <!-- Notes -->
  {#if (todo.notes || isEditing) && (isExpanded || isEditing)}
    <div class="todo-notes">
      {#if isEditing}
        <label class="setting-label">Notes</label>
        <textarea 
          class="edit-notes-input"
          bind:value={editNotes}
          placeholder="Add notes..."
          rows="3"
        ></textarea>
      {:else if todo.notes}
        <div class="notes-content">
          <h4 class="notes-title">Notes</h4>
          <p class:struck={todo.isCompleted}>{todo.notes}</p>
        </div>
      {/if}
    </div>
  {/if}

  <!-- Todo Items -->
  {#if isExpanded && !isEditing}
    <div class="todo-items">
      <div class="items-header">
        <h4 class="items-title">Sub-items</h4>
        <button class="add-item-btn" on:click={() => showAddItem = !showAddItem}>
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
          </svg>
          Add Item
        </button>
      </div>

      {#if showAddItem}
        <div class="add-item-form">
          <input 
            class="add-item-input"
            bind:value={newItemTitle}
            placeholder="New item title..."
            on:keydown={(e) => e.key === 'Enter' && handleAddItem()}
            autofocus
          />
          <button class="add-item-submit" on:click={handleAddItem}>Add</button>
        </div>
      {/if}

      <div class="items-list">
        {#each items as item (item.id)}
          <TodoItem 
            {item}
            on:update={handleItemUpdate}
            on:toggle={handleItemToggle}
            on:delete={handleItemDelete}
          />
        {/each}
      </div>
    </div>
  {/if}
</div>

<style>
  .todo-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .todo-card:hover {
    border-color: rgba(66, 153, 225, 0.4);
    box-shadow: 0 8px 25px rgba(66, 153, 225, 0.15);
    transform: translateY(-2px);
  }

  .todo-card.completed {
    background: rgba(247, 250, 252, 0.9);
    border-color: rgba(160, 174, 192, 0.5);
  }

  .todo-card.overdue {
    border-color: rgba(245, 101, 101, 0.5);
    background: rgba(254, 178, 178, 0.05);
  }

  .todo-card.editing {
    border-color: #4299e1;
    box-shadow: 0 0 0 4px rgba(66, 153, 225, 0.1);
  }

  .todo-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 1rem;
  }

  .header-left {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    flex: 1;
  }

  .checkbox {
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid #cbd5e0;
    border-radius: 8px;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-top: 0.125rem;
  }

  .checkbox:hover {
    border-color: #4299e1;
    background: rgba(66, 153, 225, 0.05);
  }

  .checkbox.checked {
    background: #4299e1;
    border-color: #4299e1;
  }

  .checkbox:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .check-icon {
    width: 1rem;
    height: 1rem;
    color: white;
    animation: checkPop 0.2s ease;
  }

  @keyframes checkPop {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
  }

  .todo-info {
    flex: 1;
  }

  .todo-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 0.5rem 0;
    line-height: 1.4;
    transition: all 0.3s ease;
  }

  .todo-title.struck {
    text-decoration: line-through;
    color: #a0aec0;
  }

  .edit-title-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid rgba(66, 153, 225, 0.3);
    border-radius: 12px;
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    background: rgba(255, 255, 255, 0.95);
    margin-bottom: 0.5rem;
  }

  .edit-title-input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .todo-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    align-items: center;
  }

  .priority-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: capitalize;
  }

  .due-date {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.25rem 0.75rem;
    background: rgba(66, 153, 225, 0.1);
    color: #3182ce;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .due-date.overdue {
    background: rgba(245, 101, 101, 0.1);
    color: #c53030;
  }

  .calendar-icon {
    width: 0.875rem;
    height: 0.875rem;
  }

  .recurrence-badge {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.25rem 0.75rem;
    background: rgba(128, 90, 213, 0.1);
    color: #805ad5;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
  }

  .repeat-icon {
    width: 0.875rem;
    height: 0.875rem;
  }

  .items-count {
    padding: 0.25rem 0.75rem;
    background: rgba(72, 187, 120, 0.1);
    color: #38a169;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .header-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
  }

  .action-btn {
    width: 2.25rem;
    height: 2.25rem;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
  }

  .action-btn:hover {
    opacity: 1;
    transform: scale(1.05);
  }

  .action-btn svg {
    width: 1.125rem;
    height: 1.125rem;
  }

  .expand-btn {
    background: rgba(160, 174, 192, 0.1);
    color: #718096;
  }

  .expand-btn:hover {
    background: rgba(160, 174, 192, 0.2);
  }

  .expand-icon {
    transition: transform 0.2s ease;
  }

  .expand-icon.rotated {
    transform: rotate(180deg);
  }

  .edit-btn {
    background: rgba(66, 153, 225, 0.1);
    color: #3182ce;
  }

  .edit-btn:hover {
    background: rgba(66, 153, 225, 0.2);
  }

  .delete-btn {
    background: rgba(245, 101, 101, 0.1);
    color: #e53e3e;
  }

  .delete-btn:hover {
    background: rgba(245, 101, 101, 0.2);
  }

  .save-btn {
    background: rgba(72, 187, 120, 0.1);
    color: #38a169;
  }

  .save-btn:hover {
    background: rgba(72, 187, 120, 0.2);
  }

  .cancel-btn {
    background: rgba(160, 174, 192, 0.1);
    color: #718096;
  }

  .cancel-btn:hover {
    background: rgba(160, 174, 192, 0.2);
  }

  .todo-description {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(226, 232, 240, 0.8);
  }

  .todo-description p {
    color: #718096;
    line-height: 1.6;
    margin: 0;
    transition: all 0.3s ease;
  }

  .todo-description p.struck {
    text-decoration: line-through;
    color: #cbd5e0;
  }

  .edit-description-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid rgba(66, 153, 225, 0.2);
    border-radius: 12px;
    font-size: 0.875rem;
    color: #2d3748;
    background: rgba(255, 255, 255, 0.95);
    resize: vertical;
    min-height: 4rem;
  }

  .edit-description-input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .edit-settings {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(226, 232, 240, 0.8);
    display: grid;
    gap: 1rem;
  }

  .setting-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .setting-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #4a5568;
  }

  .priority-select {
    padding: 0.75rem 1rem;
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.95);
    color: #2d3748;
    font-size: 0.875rem;
  }

  .priority-select:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .todo-notes {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(226, 232, 240, 0.8);
  }

  .notes-content {
    background: rgba(66, 153, 225, 0.05);
    border: 1px solid rgba(66, 153, 225, 0.1);
    border-radius: 12px;
    padding: 1rem;
  }

  .notes-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4a5568;
    margin: 0 0 0.5rem 0;
  }

  .notes-content p {
    color: #718096;
    line-height: 1.6;
    margin: 0;
    transition: all 0.3s ease;
  }

  .notes-content p.struck {
    text-decoration: line-through;
    color: #cbd5e0;
  }

  .edit-notes-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid rgba(66, 153, 225, 0.2);
    border-radius: 12px;
    font-size: 0.875rem;
    color: #2d3748;
    background: rgba(255, 255, 255, 0.95);
    resize: vertical;
    min-height: 4rem;
  }

  .edit-notes-input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .todo-items {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(226, 232, 240, 0.8);
  }

  .items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .items-title {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
  }

  .add-item-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: rgba(66, 153, 225, 0.1);
    border: 2px solid rgba(66, 153, 225, 0.2);
    border-radius: 10px;
    color: #3182ce;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .add-item-btn:hover {
    background: rgba(66, 153, 225, 0.15);
    border-color: rgba(66, 153, 225, 0.3);
  }

  .add-item-btn svg {
    width: 1rem;
    height: 1rem;
  }

  .add-item-form {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .add-item-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid rgba(66, 153, 225, 0.3);
    border-radius: 10px;
    font-size: 0.875rem;
    background: rgba(255, 255, 255, 0.95);
  }

  .add-item-input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .add-item-submit {
    padding: 0.75rem 1.5rem;
    background: #4299e1;
    color: white;
    border: none;
    border-radius: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .add-item-submit:hover {
    background: #3182ce;
  }

  .items-list {
    space-y: 0.75rem;
  }

  @media (max-width: 768px) {
    .todo-card {
      padding: 1rem;
      margin-bottom: 1rem;
    }

    .todo-header {
      flex-direction: column;
      gap: 0.75rem;
    }

    .header-left {
      gap: 0.75rem;
    }

    .header-actions {
      align-self: flex-end;
    }

    .todo-meta {
      gap: 0.5rem;
    }

    .action-btn {
      width: 2rem;
      height: 2rem;
    }

    .action-btn svg {
      width: 1rem;
      height: 1rem;
    }

    .edit-settings {
      grid-template-columns: 1fr;
    }

    .items-header {
      flex-direction: column;
      align-items: stretch;
      gap: 0.75rem;
    }

    .add-item-form {
      flex-direction: column;
    }
  }
</style>