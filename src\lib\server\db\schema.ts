import { pgTable, text, timestamp, integer, boolean, uuid } from 'drizzle-orm/pg-core';

export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: text('email').notNull().unique(),
  password: text('password').notNull(),
  name: text('name'),
  isVerified: boolean('is_verified').default(false),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const sessions = pgTable('sessions', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  token: text('token').notNull().unique(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
});

export const otpRequests = pgTable('otp_requests', {
  id: uuid('id').primaryKey().defaultRandom(),
  requestId: uuid('request_id').notNull().unique(),
  email: text('email').notNull(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }),
  code: text('code').notNull(),
  purpose: text('purpose').default('registration'), // 'registration' or 'password_reset'
  attempts: integer('attempts').default(0),
  isUsed: boolean('is_used').default(false),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
});

export const emailLimits = pgTable('email_limits', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: text('email').notNull(),
  requestCount: integer('request_count').default(0),
  lastRequestDate: timestamp('last_request_date').defaultNow(),
  createdAt: timestamp('created_at').defaultNow(),
});

export const officeFlowLinks = pgTable('office_flow_links', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  officeFlowUserId: text('office_flow_user_id').notNull(),
  officeFlowEmail: text('office_flow_email').notNull(),
  officeFlowName: text('office_flow_name'),
  officeFlowAvatar: text('office_flow_avatar'),
  officeFlowDepartment: text('office_flow_department'),
  officeFlowPosition: text('office_flow_position'),
  accessToken: text('access_token'),
  refreshToken: text('refresh_token'),
  tokenExpiresAt: timestamp('token_expires_at'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Todo lists table
export const todoLists = pgTable('todo_lists', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  title: text('title').notNull(),
  description: text('description'),
  notes: text('notes'),
  priority: text('priority').default('medium'), // 'low', 'medium', 'high', 'urgent'
  isCompleted: boolean('is_completed').default(false),
  completedAt: timestamp('completed_at'),
  dueDate: timestamp('due_date'),
  
  // Flexible recurrence configuration
  recurrenceType: text('recurrence_type'), // 'none', 'daily', 'weekly', 'monthly', 'yearly', 'custom'
  recurrencePattern: text('recurrence_pattern'), // JSON string storing complex patterns
  /*
  Example recurrence patterns:
  - Every Monday: {"type": "weekly", "days": [1]}
  - Every month on the 15th: {"type": "monthly", "day": 15}
  - Every month on the last day: {"type": "monthly", "day": "last"}
  - Every month on the 2nd Friday: {"type": "monthly", "weekday": 5, "occurrence": 2}
  - Every 3 days: {"type": "interval", "days": 3}
  - Multiple days per week: {"type": "weekly", "days": [1, 3, 5]}
  */
  
  nextDueDate: timestamp('next_due_date'), // For recurring tasks
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Todo sub-items table
export const todoItems = pgTable('todo_items', {
  id: uuid('id').primaryKey().defaultRandom(),
  todoListId: uuid('todo_list_id').notNull().references(() => todoLists.id, { onDelete: 'cascade' }),
  title: text('title').notNull(),
  description: text('description'),
  isCompleted: boolean('is_completed').default(false),
  completedAt: timestamp('completed_at'),
  orderIndex: integer('order_index').default(0),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Email notification settings
export const emailSettings = pgTable('email_settings', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  isEnabled: boolean('is_enabled').default(true),
  
  // When to send emails
  sendFrequency: text('send_frequency').default('weekly'), // 'daily', 'weekly', 'monthly', 'custom'
  sendPattern: text('send_pattern'), // JSON string for custom patterns
  /*
  Example send patterns:
  - Every Monday: {"type": "weekly", "day": 1}
  - Every 3 days: {"type": "interval", "days": 3}
  - First day of month: {"type": "monthly", "day": 1}
  */
  
  // What time period to include in email
  lookAheadDays: integer('look_ahead_days').default(10), // How many days in advance to include
  includeOverdue: boolean('include_overdue').default(true),
  includeCompleted: boolean('include_completed').default(false),
  
  lastSentAt: timestamp('last_sent_at'),
  nextSendAt: timestamp('next_send_at'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Email send history
export const emailHistory = pgTable('email_history', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  emailType: text('email_type').default('todo_digest'), // 'todo_digest', 'reminder', etc.
  recipientEmail: text('recipient_email').notNull(),
  subject: text('subject').notNull(),
  content: text('content').notNull(),
  isSuccess: boolean('is_success').default(false),
  errorMessage: text('error_message'),
  sentAt: timestamp('sent_at').defaultNow(),
});

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Session = typeof sessions.$inferSelect;
export type NewSession = typeof sessions.$inferInsert;
export type OtpRequest = typeof otpRequests.$inferSelect;
export type NewOtpRequest = typeof otpRequests.$inferInsert;
export type EmailLimit = typeof emailLimits.$inferSelect;
export type NewEmailLimit = typeof emailLimits.$inferInsert;
export type OfficeFlowLink = typeof officeFlowLinks.$inferSelect;
export type NewOfficeFlowLink = typeof officeFlowLinks.$inferInsert;

export type TodoList = typeof todoLists.$inferSelect;
export type NewTodoList = typeof todoLists.$inferInsert;
export type TodoItem = typeof todoItems.$inferSelect;  
export type NewTodoItem = typeof todoItems.$inferInsert;
export type EmailSettings = typeof emailSettings.$inferSelect;
export type NewEmailSettings = typeof emailSettings.$inferInsert;
export type EmailHistory = typeof emailHistory.$inferSelect;
export type NewEmailHistory = typeof emailHistory.$inferInsert;
