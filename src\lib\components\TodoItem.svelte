<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  export let item = {
    id: '',
    title: '',
    description: '',
    isCompleted: false,
    orderIndex: 0
  };
  export let isEditing = false;

  const dispatch = createEventDispatcher();
  
  let editTitle = item.title;
  let editDescription = item.description;

  function handleToggleComplete() {
    dispatch('toggle', {
      id: item.id,
      isCompleted: !item.isCompleted
    });
  }

  function handleSaveEdit() {
    if (editTitle.trim()) {
      dispatch('update', {
        id: item.id,
        title: editTitle.trim(),
        description: editDescription.trim()
      });
      isEditing = false;
    }
  }

  function handleCancelEdit() {
    editTitle = item.title;
    editDescription = item.description;
    isEditing = false;
  }

  function handleDelete() {
    dispatch('delete', { id: item.id });
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSaveEdit();
    } else if (event.key === 'Escape') {
      handleCancelEdit();
    }
  }
</script>

<div class="todo-item" class:completed={item.isCompleted} class:editing={isEditing}>
  <div class="item-content">
    <!-- Checkbox -->
    <button 
      class="checkbox"
      class:checked={item.isCompleted}
      on:click={handleToggleComplete}
      disabled={isEditing}
    >
      {#if item.isCompleted}
        <svg class="check-icon" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
        </svg>
      {/if}
    </button>

    <!-- Content -->
    <div class="item-details">
      {#if isEditing}
        <input 
          class="edit-title-input"
          bind:value={editTitle}
          on:keydown={handleKeydown}
          placeholder="Item title..."
          autofocus
        />
        <textarea 
          class="edit-description-input"
          bind:value={editDescription}
          on:keydown={handleKeydown}
          placeholder="Description (optional)..."
          rows="2"
        ></textarea>
      {:else}
        <div class="item-title" class:struck={item.isCompleted}>
          {item.title}
        </div>
        {#if item.description}
          <div class="item-description" class:struck={item.isCompleted}>
            {item.description}
          </div>
        {/if}
      {/if}
    </div>

    <!-- Actions -->
    <div class="item-actions">
      {#if isEditing}
        <button class="action-btn save-btn" on:click={handleSaveEdit}>
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </button>
        <button class="action-btn cancel-btn" on:click={handleCancelEdit}>
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      {:else}
        <button class="action-btn edit-btn" on:click={() => isEditing = true}>
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
          </svg>
        </button>
        <button class="action-btn delete-btn" on:click={handleDelete}>
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd" />
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </button>
      {/if}
    </div>
  </div>
</div>

<style>
  .todo-item {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.3s ease;
    margin-bottom: 0.75rem;
  }

  .todo-item:hover {
    border-color: rgba(66, 153, 225, 0.3);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.1);
    transform: translateY(-1px);
  }

  .todo-item.completed {
    background: rgba(247, 250, 252, 0.8);
    border-color: rgba(226, 232, 240, 0.6);
  }

  .todo-item.editing {
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .item-content {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .checkbox {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #cbd5e0;
    border-radius: 6px;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-top: 0.125rem;
  }

  .checkbox:hover {
    border-color: #4299e1;
    background: rgba(66, 153, 225, 0.05);
  }

  .checkbox.checked {
    background: #4299e1;
    border-color: #4299e1;
  }

  .checkbox:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .check-icon {
    width: 0.875rem;
    height: 0.875rem;
    color: white;
    animation: checkPop 0.2s ease;
  }

  @keyframes checkPop {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
  }

  .item-details {
    flex: 1;
    min-width: 0;
  }

  .item-title {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 0.25rem;
    line-height: 1.4;
    transition: all 0.3s ease;
  }

  .item-title.struck {
    text-decoration: line-through;
    color: #a0aec0;
  }

  .item-description {
    font-size: 0.875rem;
    color: #718096;
    line-height: 1.4;
    transition: all 0.3s ease;
  }

  .item-description.struck {
    text-decoration: line-through;
    color: #cbd5e0;
  }

  .edit-title-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 2px solid rgba(66, 153, 225, 0.3);
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #2d3748;
    background: rgba(255, 255, 255, 0.95);
    margin-bottom: 0.5rem;
    transition: border-color 0.2s ease;
  }

  .edit-title-input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .edit-description-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 2px solid rgba(66, 153, 225, 0.2);
    border-radius: 8px;
    font-size: 0.875rem;
    color: #2d3748;
    background: rgba(255, 255, 255, 0.95);
    resize: vertical;
    min-height: 2.5rem;
    transition: border-color 0.2s ease;
  }

  .edit-description-input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .item-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
  }

  .action-btn {
    width: 2rem;
    height: 2rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.6;
  }

  .action-btn:hover {
    opacity: 1;
    transform: scale(1.05);
  }

  .action-btn svg {
    width: 1rem;
    height: 1rem;
  }

  .edit-btn {
    background: rgba(66, 153, 225, 0.1);
    color: #3182ce;
  }

  .edit-btn:hover {
    background: rgba(66, 153, 225, 0.2);
  }

  .delete-btn {
    background: rgba(245, 101, 101, 0.1);
    color: #e53e3e;
  }

  .delete-btn:hover {
    background: rgba(245, 101, 101, 0.2);
  }

  .save-btn {
    background: rgba(72, 187, 120, 0.1);
    color: #38a169;
  }

  .save-btn:hover {
    background: rgba(72, 187, 120, 0.2);
  }

  .cancel-btn {
    background: rgba(160, 174, 192, 0.1);
    color: #718096;
  }

  .cancel-btn:hover {
    background: rgba(160, 174, 192, 0.2);
  }

  @media (max-width: 768px) {
    .todo-item {
      padding: 0.75rem;
    }

    .item-content {
      gap: 0.5rem;
    }

    .action-btn {
      width: 1.75rem;
      height: 1.75rem;
    }

    .action-btn svg {
      width: 0.875rem;
      height: 0.875rem;
    }
  }
</style>