import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getEmailSettingsByUserId, createEmailSettings, updateEmailSettings } from '$lib/server/db/operations.js';

export const GET: RequestHandler = async ({ locals }) => {
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    let settings = await getEmailSettingsByUserId(locals.user.id);
    
    if (!settings) {
      // Create default settings
      settings = await createEmailSettings({
        userId: locals.user.id,
        isEnabled: false,
        sendFrequency: 'weekly',
        sendPattern: JSON.stringify({ type: 'weekly', day: 1 }), // Monday
        lookAheadDays: 10,
        includeOverdue: true,
        includeCompleted: false,
        nextSendAt: null
      });
    }

    return json({ settings });
  } catch (error) {
    console.error('Failed to fetch email settings:', error);
    return json({ error: 'Failed to fetch email settings' }, { status: 500 });
  }
};

export const PUT: RequestHandler = async ({ request, locals }) => {
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { isEnabled, sendFrequency, sendPattern, lookAheadDays, includeOverdue, includeCompleted } = body;

    const updateData: any = {};
    if (isEnabled !== undefined) updateData.isEnabled = isEnabled;
    if (sendFrequency !== undefined) updateData.sendFrequency = sendFrequency;
    if (sendPattern !== undefined) updateData.sendPattern = JSON.stringify(sendPattern);
    if (lookAheadDays !== undefined) updateData.lookAheadDays = lookAheadDays;
    if (includeOverdue !== undefined) updateData.includeOverdue = includeOverdue;
    if (includeCompleted !== undefined) updateData.includeCompleted = includeCompleted;

    // Calculate next send time if frequency changed
    if (sendPattern) {
      updateData.nextSendAt = calculateNextSendTime(sendPattern);
    }

    const updatedSettings = await updateEmailSettings(locals.user.id, updateData);
    if (!updatedSettings) {
      return json({ error: 'Settings not found' }, { status: 404 });
    }

    return json({ settings: updatedSettings });
  } catch (error) {
    console.error('Failed to update email settings:', error);
    return json({ error: 'Failed to update email settings' }, { status: 500 });
  }
};

function calculateNextSendTime(pattern: any): Date {
  const now = new Date();
  const nextSend = new Date();

  switch (pattern.type) {
    case 'weekly':
      // pattern.day: 0=Sunday, 1=Monday, etc.
      const currentDay = now.getDay();
      const targetDay = pattern.day;
      let daysUntilTarget = targetDay - currentDay;
      if (daysUntilTarget <= 0) {
        daysUntilTarget += 7; // Next week
      }
      nextSend.setDate(now.getDate() + daysUntilTarget);
      nextSend.setHours(9, 0, 0, 0); // 9 AM
      break;

    case 'interval':
      nextSend.setDate(now.getDate() + pattern.days);
      nextSend.setHours(9, 0, 0, 0);
      break;

    case 'monthly':
      if (pattern.day === 1) {
        // First day of next month
        nextSend.setMonth(now.getMonth() + 1, 1);
      } else {
        nextSend.setDate(now.getDate() + 30); // Rough monthly
      }
      nextSend.setHours(9, 0, 0, 0);
      break;

    default:
      nextSend.setDate(now.getDate() + 7); // Default weekly
      nextSend.setHours(9, 0, 0, 0);
  }

  return nextSend;
}