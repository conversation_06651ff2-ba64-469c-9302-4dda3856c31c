<script lang="ts">
  import { onMount } from 'svelte';
  import { fade, fly } from 'svelte/transition';
  import type { PageData } from './$types';
  import TodoCard from '$lib/components/TodoCard.svelte';
  import DatePicker from '$lib/components/DatePicker.svelte';

  export let data: PageData;

  let todos = [];
  let loading = true;
  let showCreateForm = false;
  let creating = false;

  // Create form data
  let newTodo = {
    title: '',
    description: '',
    notes: '',
    priority: 'medium',
    dueDate: null,
    recurrenceType: 'none',
    recurrencePattern: null
  };

  // Metrics calculation
  $: todayTodos = todos.filter(todo => {
    if (!todo.dueDate) return false;
    const today = new Date();
    const dueDate = new Date(todo.dueDate);
    return dueDate.toDateString() === today.toDateString();
  });

  $: overdueTodos = todos.filter(todo => {
    if (!todo.dueDate || todo.isCompleted) return false;
    return new Date(todo.dueDate) < new Date();
  });

  $: thisWeekTodos = todos.filter(todo => {
    if (!todo.dueDate) return false;
    const today = new Date();
    const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
    const dueDate = new Date(todo.dueDate);
    return dueDate >= today && dueDate <= weekFromNow;
  });

  $: completedTodos = todos.filter(todo => todo.isCompleted);
  $: completionRate = todos.length > 0 ? Math.round((completedTodos.length / todos.length) * 100) : 0;

  $: metrics = [
    { 
      value: todayTodos.length.toString(), 
      label: 'Today', 
      trend: '+2', 
      trendType: 'up' 
    },
    { 
      value: overdueTodos.length.toString(), 
      label: 'Overdue', 
      trend: overdueTodos.length > 0 ? `+${overdueTodos.length}` : '0', 
      trendType: overdueTodos.length > 0 ? 'down' : 'up' 
    },
    { 
      value: thisWeekTodos.length.toString(), 
      label: 'This Week', 
      trend: '+5', 
      trendType: 'up' 
    },
    { 
      value: `${completionRate}%`, 
      label: 'Completed', 
      trend: '+3%', 
      trendType: 'up' 
    }
  ];

  onMount(() => {
    loadTodos();
  });

  async function loadTodos() {
    try {
      const response = await fetch('/api/todos');
      if (response.ok) {
        const result = await response.json();
        todos = result.todos || [];
      }
    } catch (error) {
      console.error('Failed to load todos:', error);
    } finally {
      loading = false;
    }
  }

  async function createTodo() {
    if (!newTodo.title.trim()) return;
    
    creating = true;
    try {
      const response = await fetch('/api/todos', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newTodo)
      });

      if (response.ok) {
        const result = await response.json();
        todos = [result.todo, ...todos];
        resetCreateForm();
        showCreateForm = false;
      }
    } catch (error) {
      console.error('Failed to create todo:', error);
    } finally {
      creating = false;
    }
  }

  function resetCreateForm() {
    newTodo = {
      title: '',
      description: '',
      notes: '',
      priority: 'medium',
      dueDate: null,
      recurrenceType: 'none',
      recurrencePattern: null
    };
  }

  async function handleTodoUpdate(event) {
    const { id, ...updateData } = event.detail;
    try {
      const response = await fetch(`/api/todos/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });

      if (response.ok) {
        const result = await response.json();
        todos = todos.map(todo => todo.id === id ? result.todo : todo);
      }
    } catch (error) {
      console.error('Failed to update todo:', error);
    }
  }

  async function handleTodoToggle(event) {
    const { id, isCompleted } = event.detail;
    try {
      const response = await fetch(`/api/todos/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isCompleted })
      });

      if (response.ok) {
        const result = await response.json();
        todos = todos.map(todo => todo.id === id ? result.todo : todo);
      }
    } catch (error) {
      console.error('Failed to toggle todo:', error);
    }
  }

  async function handleTodoDelete(event) {
    const { id } = event.detail;
    try {
      const response = await fetch(`/api/todos/${id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        todos = todos.filter(todo => todo.id !== id);
      }
    } catch (error) {
      console.error('Failed to delete todo:', error);
    }
  }

  async function handleAddItem(event) {
    const { todoId, title } = event.detail;
    try {
      const response = await fetch(`/api/todos/${todoId}/items`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title })
      });

      if (response.ok) {
        await loadTodoItems(todoId);
      }
    } catch (error) {
      console.error('Failed to add item:', error);
    }
  }

  async function handleUpdateItem(event) {
    const { todoId, id, ...updateData } = event.detail;
    try {
      const response = await fetch(`/api/todos/${todoId}/items/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });

      if (response.ok) {
        await loadTodoItems(todoId);
      }
    } catch (error) {
      console.error('Failed to update item:', error);
    }
  }

  async function handleToggleItem(event) {
    const { todoId, id, isCompleted } = event.detail;
    try {
      const response = await fetch(`/api/todos/${todoId}/items/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isCompleted })
      });

      if (response.ok) {
        await loadTodoItems(todoId);
      }
    } catch (error) {
      console.error('Failed to toggle item:', error);
    }
  }

  async function handleDeleteItem(event) {
    const { todoId, id } = event.detail;
    try {
      const response = await fetch(`/api/todos/${todoId}/items/${id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        await loadTodoItems(todoId);
      }
    } catch (error) {
      console.error('Failed to delete item:', error);
    }
  }

  async function loadTodoItems(todoId) {
    try {
      const response = await fetch(`/api/todos/${todoId}`);
      if (response.ok) {
        const result = await response.json();
        todos = todos.map(todo => 
          todo.id === todoId ? { ...todo, items: result.items } : todo
        );
      }
    } catch (error) {
      console.error('Failed to load todo items:', error);
    }
  }

  function getCurrentDate() {
    const options = { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    };
    return new Date().toLocaleDateString('en-US', options);
  }
</script>

<div class="page-header">
  <h1 class="page-title">Dashboard</h1>
  <p class="page-subtitle">{getCurrentDate()}</p>
</div>

<div class="metrics-row">
  {#each metrics as metric}
    <div class="metric-card" in:fly={{ y: 20, duration: 300, delay: metrics.indexOf(metric) * 100 }}>
      <div class="metric-trend trend-{metric.trendType}">{metric.trend}</div>
      <div class="metric-value">{metric.value}</div>
      <div class="metric-label">{metric.label}</div>
    </div>
  {/each}
</div>

<div class="content-grid">
  <div class="main-panel">
    <div class="panel-header">
      <h2 class="panel-title">
        <span class="status-dot"></span>
        Your Todos
      </h2>
      <button 
        class="create-todo-btn"
        on:click={() => showCreateForm = !showCreateForm}
      >
        <svg viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
        </svg>
        New Todo
      </button>
    </div>

    <!-- Create form -->
    {#if showCreateForm}
      <div class="create-form" in:fly={{ y: -20, duration: 300 }}>
        <div class="form-group">
          <input 
            class="form-input"
            bind:value={newTodo.title}
            placeholder="What needs to be done?"
            autofocus
          />
        </div>

        <div class="form-group">
          <textarea 
            class="form-textarea"
            bind:value={newTodo.description}
            placeholder="Description (optional)..."
            rows="3"
          ></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">Priority</label>
            <select class="form-select" bind:value={newTodo.priority}>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="urgent">Urgent</option>
            </select>
          </div>

          <div class="form-group flex-1">
            <label class="form-label">Due Date & Recurrence</label>
            <DatePicker 
              bind:selectedDate={newTodo.dueDate}
              bind:recurrenceType={newTodo.recurrenceType}
              bind:recurrencePattern={newTodo.recurrencePattern}
              placeholder="Set due date..."
            />
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">Notes</label>
          <textarea 
            class="form-textarea"
            bind:value={newTodo.notes}
            placeholder="Add notes..."
            rows="3"
          ></textarea>
        </div>

        <div class="form-actions">
          <button 
            class="form-btn primary"
            on:click={createTodo}
            disabled={creating || !newTodo.title.trim()}
          >
            {creating ? 'Creating...' : 'Create Todo'}
          </button>
          <button 
            class="form-btn secondary"
            on:click={() => { showCreateForm = false; resetCreateForm(); }}
          >
            Cancel
          </button>
        </div>
      </div>
    {/if}

    <!-- Todos list -->
    <div class="todos-list">
      {#if loading}
        <div class="loading-state">
          <div class="loading-spinner"></div>
          <p>Loading your todos...</p>
        </div>
      {:else if todos.length === 0}
        <div class="empty-state">
          <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3>No todos yet</h3>
          <p>Create your first todo to get started with organizing your tasks.</p>
          <button 
            class="empty-action-btn"
            on:click={() => showCreateForm = true}
          >
            Create Your First Todo
          </button>
        </div>
      {:else}
        {#each todos as todo, index (todo.id)}
          <div in:fly={{ y: 20, duration: 300, delay: index * 50 }}>
            <TodoCard 
              {todo}
              items={todo.items || []}
              on:update={handleTodoUpdate}
              on:toggle={handleTodoToggle}
              on:delete={handleTodoDelete}
              on:addItem={handleAddItem}
              on:updateItem={handleUpdateItem}
              on:toggleItem={handleToggleItem}
              on:deleteItem={handleDeleteItem}
            />
          </div>
        {/each}
      {/if}
    </div>
  </div>

  <div class="side-panel">
    <div class="widget">
      <h3 class="widget-title">Quick Actions</h3>
      <button 
        class="action-btn"
        on:click={() => showCreateForm = true}
      >
        + New Todo
      </button>
      <a href="/dashboard/settings" class="action-btn secondary">
        Email Settings
      </a>
      <button class="action-btn secondary">View Calendar</button>
    </div>

    <div class="widget">
      <h3 class="widget-title">Upcoming Deadlines</h3>
      <div class="upcoming-list">
        {#each todos.filter(t => t.dueDate && !t.isCompleted).slice(0, 5) as todo}
          <div class="upcoming-item">
            <div class="upcoming-date">
              {new Date(todo.dueDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
            </div>
            <div class="upcoming-task">{todo.title}</div>
          </div>
        {/each}
      </div>
    </div>
  </div>
</div>

<style>
  .page-header {
    margin-bottom: 3rem;
  }

  .page-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
  }

  .page-subtitle {
    color: #718096;
    font-size: 1.125rem;
    margin: 0;
  }

  .metrics-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
  }

  .metric-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .metric-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4299e1, #63b3ed);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .metric-card:hover {
    border-color: rgba(66, 153, 225, 0.3);
    box-shadow: 0 8px 25px rgba(66, 153, 225, 0.15);
    transform: translateY(-2px);
  }

  .metric-card:hover::before {
    opacity: 1;
  }

  .metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.25rem;
  }

  .metric-label {
    color: #718096;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .metric-trend {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-weight: 600;
  }

  .trend-up {
    background: #f0fff4;
    color: #38a169;
  }

  .trend-down {
    background: #fed7d7;
    color: #e53e3e;
  }

  .content-grid {
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: 2rem;
  }

  .main-panel {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .panel-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .panel-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1a202c;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4299e1;
  }

  .create-todo-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  }

  .create-todo-btn:hover {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
  }

  .create-todo-btn svg {
    width: 1rem;
    height: 1rem;
  }

  .create-form {
    padding: 2rem;
    background: rgba(66, 153, 225, 0.05);
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .form-row {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .flex-1 {
    flex: 1;
  }

  .form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 0.5rem;
  }

  .form-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.95);
    transition: border-color 0.2s ease;
  }

  .form-input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .form-textarea {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    font-size: 0.875rem;
    background: rgba(255, 255, 255, 0.95);
    resize: vertical;
    min-height: 3rem;
    transition: border-color 0.2s ease;
  }

  .form-textarea:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .form-select {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    font-size: 0.875rem;
    background: rgba(255, 255, 255, 0.95);
    transition: border-color 0.2s ease;
  }

  .form-select:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
  }

  .form-btn {
    padding: 0.875rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .form-btn.primary {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    border: none;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  }

  .form-btn.primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
  }

  .form-btn.primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .form-btn.secondary {
    background: rgba(160, 174, 192, 0.1);
    color: #4a5568;
    border: 2px solid rgba(160, 174, 192, 0.3);
  }

  .form-btn.secondary:hover {
    background: rgba(160, 174, 192, 0.2);
  }

  .todos-list {
    padding: 2rem;
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    color: #718096;
  }

  .loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid rgba(66, 153, 225, 0.2);
    border-top: 3px solid #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
    color: #718096;
  }

  .empty-icon {
    width: 4rem;
    height: 4rem;
    color: #cbd5e0;
    margin-bottom: 1rem;
  }

  .empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #4a5568;
    margin: 0 0 0.5rem 0;
  }

  .empty-state p {
    font-size: 0.875rem;
    margin: 0 0 2rem 0;
    max-width: 24rem;
  }

  .empty-action-btn {
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  }

  .empty-action-btn:hover {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
  }

  .side-panel {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .widget {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .widget-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 1rem;
  }

  .action-btn {
    display: block;
    width: 100%;
    padding: 0.875rem;
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0.75rem;
    text-decoration: none;
    text-align: center;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  }

  .action-btn:hover {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
  }

  .action-btn.secondary {
    background: rgba(247, 250, 252, 0.8);
    color: #4a5568;
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .action-btn.secondary:hover {
    background: rgba(237, 242, 247, 0.9);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .upcoming-list {
    space-y: 0.75rem;
  }

  .upcoming-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f7fafc;
  }

  .upcoming-item:last-child {
    border-bottom: none;
  }

  .upcoming-date {
    font-size: 0.75rem;
    color: #718096;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25rem;
  }

  .upcoming-task {
    font-weight: 500;
    color: #1a202c;
    font-size: 0.875rem;
  }

  @media (max-width: 768px) {
    .content-grid {
      grid-template-columns: 1fr;
    }

    .metrics-row {
      grid-template-columns: repeat(2, 1fr);
    }

    .page-title {
      font-size: 1.875rem;
    }

    .panel-header {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
    }

    .create-form {
      padding: 1.5rem;
    }

    .form-row {
      grid-template-columns: 1fr;
    }

    .form-actions {
      flex-direction: column-reverse;
    }

    .todos-list {
      padding: 1rem;
    }
  }
</style>
