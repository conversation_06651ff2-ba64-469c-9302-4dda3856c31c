import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getTodoListWithItems, updateTodoList, deleteTodoList } from '$lib/server/db/operations.js';

export const GET: RequestHandler = async ({ params, locals }) => {
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const result = await getTodoListWithItems(params.id, locals.user.id);
    if (!result) {
      return json({ error: 'Todo not found' }, { status: 404 });
    }
    return json(result);
  } catch (error) {
    console.error('Failed to fetch todo:', error);
    return json({ error: 'Failed to fetch todo' }, { status: 500 });
  }
};

export const PUT: RequestHandler = async ({ params, request, locals }) => {
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { title, description, notes, priority, dueDate, recurrenceType, recurrencePattern, isCompleted } = body;

    const updateData: any = {};
    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (notes !== undefined) updateData.notes = notes;
    if (priority !== undefined) updateData.priority = priority;
    if (dueDate !== undefined) updateData.dueDate = dueDate ? new Date(dueDate) : null;
    if (recurrenceType !== undefined) updateData.recurrenceType = recurrenceType;
    if (recurrencePattern !== undefined) updateData.recurrencePattern = recurrencePattern ? JSON.stringify(recurrencePattern) : null;
    if (isCompleted !== undefined) {
      updateData.isCompleted = isCompleted;
      updateData.completedAt = isCompleted ? new Date() : null;
    }

    const updatedTodo = await updateTodoList(params.id, locals.user.id, updateData);
    if (!updatedTodo) {
      return json({ error: 'Todo not found' }, { status: 404 });
    }

    return json({ todo: updatedTodo });
  } catch (error) {
    console.error('Failed to update todo:', error);
    return json({ error: 'Failed to update todo' }, { status: 500 });
  }
};

export const DELETE: RequestHandler = async ({ params, locals }) => {
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    await deleteTodoList(params.id, locals.user.id);
    return json({ success: true });
  } catch (error) {
    console.error('Failed to delete todo:', error);
    return json({ error: 'Failed to delete todo' }, { status: 500 });
  }
};