import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { updateTodoItem, deleteTodoItem } from '$lib/server/db/operations.js';

export const PUT: RequestHandler = async ({ params, request, locals }) => {
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { title, description, isCompleted, orderIndex } = body;

    const updateData: any = {};
    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (orderIndex !== undefined) updateData.orderIndex = orderIndex;
    if (isCompleted !== undefined) {
      updateData.isCompleted = isCompleted;
      updateData.completedAt = isCompleted ? new Date() : null;
    }

    const updatedItem = await updateTodoItem(params.itemId, updateData);
    if (!updatedItem) {
      return json({ error: 'Todo item not found' }, { status: 404 });
    }

    return json({ item: updatedItem });
  } catch (error) {
    console.error('Failed to update todo item:', error);
    return json({ error: 'Failed to update todo item' }, { status: 500 });
  }
};

export const DELETE: RequestHandler = async ({ params, locals }) => {
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    await deleteTodoItem(params.itemId);
    return json({ success: true });
  } catch (error) {
    console.error('Failed to delete todo item:', error);
    return json({ error: 'Failed to delete todo item' }, { status: 500 });
  }
};