-- Migration: Add Office Flow Links table
-- Created: 2024-01-01
-- Description: Add table to store Office Flow account links

CREATE TABLE IF NOT EXISTS "office_flow_links" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"office_flow_user_id" text NOT NULL,
	"office_flow_email" text NOT NULL,
	"office_flow_name" text,
	"office_flow_avatar" text,
	"office_flow_department" text,
	"office_flow_position" text,
	"access_token" text,
	"refresh_token" text,
	"token_expires_at" timestamp,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);

-- Add foreign key constraint
ALTER TABLE "office_flow_links" ADD CONSTRAINT "office_flow_links_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS "office_flow_links_user_id_idx" ON "office_flow_links" ("user_id");
CREATE INDEX IF NOT EXISTS "office_flow_links_office_flow_user_id_idx" ON "office_flow_links" ("office_flow_user_id");
CREATE INDEX IF NOT EXISTS "office_flow_links_is_active_idx" ON "office_flow_links" ("is_active");

-- Add unique constraint to prevent duplicate active links
CREATE UNIQUE INDEX IF NOT EXISTS "office_flow_links_user_id_active_unique" ON "office_flow_links" ("user_id") WHERE "is_active" = true;
CREATE UNIQUE INDEX IF NOT EXISTS "office_flow_links_office_flow_user_id_active_unique" ON "office_flow_links" ("office_flow_user_id") WHERE "is_active" = true;
