import nodemailer from 'nodemailer';
import type { TodoList, TodoItem, User, EmailSettings } from '$lib/server/db/schema.js';
import { getTodosForEmail, getOverdueTodos, createEmailHistory } from '$lib/server/db/operations.js';

interface TodoWithItems extends TodoList {
  items?: TodoItem[];
}

export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST || 'localhost',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
  }

  async sendTodoDigest(user: User, settings: EmailSettings): Promise<boolean> {
    try {
      const now = new Date();
      const endDate = new Date(now.getTime() + settings.lookAheadDays * 24 * 60 * 60 * 1000);

      // Get upcoming todos
      const upcomingTodos = await getTodosForEmail(user.id, now, endDate);
      
      // Get overdue todos if requested
      const overdueTodos = settings.includeOverdue ? await getOverdueTodos(user.id) : [];

      // Filter completed todos if not requested
      const filteredUpcoming = settings.includeCompleted 
        ? upcomingTodos 
        : upcomingTodos.filter(todo => !todo.isCompleted);

      if (filteredUpcoming.length === 0 && overdueTodos.length === 0) {
        // No todos to send
        return true;
      }

      const emailContent = this.generateEmailContent(user, filteredUpcoming, overdueTodos, settings);
      const subject = this.generateSubject(filteredUpcoming.length, overdueTodos.length, settings.lookAheadDays);

      const mailOptions = {
        from: process.env.FROM_EMAIL || '<EMAIL>',
        to: user.email,
        subject,
        html: emailContent
      };

      await this.transporter.sendMail(mailOptions);

      // Log successful send
      await createEmailHistory({
        userId: user.id,
        emailType: 'todo_digest',
        recipientEmail: user.email,
        subject,
        content: emailContent,
        isSuccess: true
      });

      return true;
    } catch (error) {
      console.error('Failed to send email:', error);
      
      // Log failed send
      await createEmailHistory({
        userId: user.id,
        emailType: 'todo_digest',
        recipientEmail: user.email,
        subject: 'Todo Digest',
        content: '',
        isSuccess: false,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });

      return false;
    }
  }

  private generateSubject(upcomingCount: number, overdueCount: number, lookAheadDays: number): string {
    const parts = [];
    
    if (upcomingCount > 0) {
      parts.push(`${upcomingCount} upcoming task${upcomingCount === 1 ? '' : 's'}`);
    }
    
    if (overdueCount > 0) {
      parts.push(`${overdueCount} overdue task${overdueCount === 1 ? '' : 's'}`);
    }

    if (parts.length === 0) {
      return `Your ${lookAheadDays}-day task overview`;
    }

    return `📋 ${parts.join(' • ')} - ${lookAheadDays}-day overview`;
  }

  private generateEmailContent(
    user: User, 
    upcomingTodos: TodoList[], 
    overdueTodos: TodoList[], 
    settings: EmailSettings
  ): string {
    const userName = user.name || user.email.split('@')[0];
    const greeting = this.getGreeting();

    let html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your Todo Digest</title>
        <style>
            body { 
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                line-height: 1.6; 
                color: #2d3748; 
                background-color: #f7fafc;
                margin: 0;
                padding: 20px;
            }
            .container { 
                max-width: 600px; 
                margin: 0 auto; 
                background: white; 
                border-radius: 12px; 
                overflow: hidden;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            }
            .header { 
                background: linear-gradient(135deg, #4299e1, #3182ce); 
                color: white; 
                padding: 30px; 
                text-align: center; 
            }
            .header h1 { 
                margin: 0; 
                font-size: 24px; 
                font-weight: 600; 
            }
            .header p { 
                margin: 8px 0 0 0; 
                opacity: 0.9; 
                font-size: 16px; 
            }
            .content { 
                padding: 30px; 
            }
            .section { 
                margin-bottom: 30px; 
            }
            .section h2 { 
                color: #2d3748; 
                font-size: 18px; 
                font-weight: 600; 
                margin-bottom: 15px; 
                padding-bottom: 8px;
                border-bottom: 2px solid #e2e8f0;
            }
            .overdue h2 { 
                color: #c53030; 
                border-bottom-color: #fed7d7;
            }
            .todo-item { 
                background: #f8f9fa; 
                border-radius: 8px; 
                padding: 15px; 
                margin-bottom: 12px; 
                border-left: 4px solid #4299e1; 
            }
            .todo-item.overdue { 
                border-left-color: #e53e3e; 
                background: #fef5f5; 
            }
            .todo-item.high-priority { 
                border-left-color: #d69e2e; 
            }
            .todo-item.urgent { 
                border-left-color: #c53030; 
            }
            .todo-title { 
                font-weight: 600; 
                font-size: 16px; 
                margin-bottom: 5px; 
            }
            .todo-meta { 
                font-size: 14px; 
                color: #718096; 
                margin-bottom: 8px; 
            }
            .todo-description { 
                font-size: 14px; 
                color: #4a5568; 
                margin-bottom: 8px; 
            }
            .todo-notes { 
                font-size: 13px; 
                color: #718096; 
                background: #edf2f7; 
                padding: 8px 12px; 
                border-radius: 6px; 
                margin-top: 8px; 
            }
            .priority-badge { 
                display: inline-block; 
                padding: 2px 8px; 
                border-radius: 12px; 
                font-size: 12px; 
                font-weight: 500; 
                text-transform: uppercase; 
            }
            .priority-low { background: #e6fffa; color: #319795; }
            .priority-medium { background: #e6fffa; color: #319795; }
            .priority-high { background: #fef5e7; color: #d69e2e; }
            .priority-urgent { background: #fed7d7; color: #c53030; }
            .footer { 
                background: #f8f9fa; 
                padding: 20px; 
                text-align: center; 
                font-size: 14px; 
                color: #718096; 
            }
            .footer a { 
                color: #4299e1; 
                text-decoration: none; 
            }
            .empty-state { 
                text-align: center; 
                padding: 40px 20px; 
                color: #718096; 
            }
            .empty-state h3 { 
                color: #4a5568; 
                margin-bottom: 8px; 
            }
            .stats { 
                display: flex; 
                justify-content: space-around; 
                margin: 20px 0; 
                background: #edf2f7; 
                border-radius: 8px; 
                padding: 15px; 
            }
            .stat { 
                text-align: center; 
            }
            .stat-number { 
                font-size: 20px; 
                font-weight: 600; 
                color: #2d3748; 
            }
            .stat-label { 
                font-size: 12px; 
                color: #718096; 
                text-transform: uppercase; 
                letter-spacing: 0.5px; 
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>📋 Your Todo Digest</h1>
                <p>${greeting}, ${userName}!</p>
            </div>
            
            <div class="content">
                <div class="stats">
                    <div class="stat">
                        <div class="stat-number">${upcomingTodos.length}</div>
                        <div class="stat-label">Upcoming</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">${overdueTodos.length}</div>
                        <div class="stat-label">Overdue</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">${settings.lookAheadDays}</div>
                        <div class="stat-label">Days Ahead</div>
                    </div>
                </div>`;

    // Overdue todos section
    if (overdueTodos.length > 0) {
      html += `
                <div class="section overdue">
                    <h2>🚨 Overdue Tasks</h2>`;
      
      overdueTodos.forEach(todo => {
        html += this.generateTodoItemHTML(todo, true);
      });
      
      html += `</div>`;
    }

    // Upcoming todos section
    if (upcomingTodos.length > 0) {
      html += `
                <div class="section">
                    <h2>📅 Upcoming Tasks (Next ${settings.lookAheadDays} Days)</h2>`;
      
      upcomingTodos.forEach(todo => {
        html += this.generateTodoItemHTML(todo, false);
      });
      
      html += `</div>`;
    }

    // Empty state
    if (upcomingTodos.length === 0 && overdueTodos.length === 0) {
      html += `
                <div class="empty-state">
                    <h3>All caught up! 🎉</h3>
                    <p>You have no upcoming tasks in the next ${settings.lookAheadDays} days.</p>
                </div>`;
    }

    html += `
            </div>
            
            <div class="footer">
                <p>
                    This digest was generated by 
                    <a href="${process.env.APP_URL || 'http://localhost:3000'}">Routine Mail</a>
                </p>
                <p>
                    <a href="${process.env.APP_URL || 'http://localhost:3000'}/dashboard/settings">
                        Update your email preferences
                    </a>
                </p>
            </div>
        </div>
    </body>
    </html>`;

    return html;
  }

  private generateTodoItemHTML(todo: TodoList, isOverdue: boolean): string {
    const dueDate = todo.dueDate ? new Date(todo.dueDate) : null;
    const priorityClass = `priority-${todo.priority || 'medium'}`;
    const itemClass = isOverdue ? 'todo-item overdue' : `todo-item ${priorityClass}`;

    let html = `
        <div class="${itemClass}">
            <div class="todo-title">${todo.title}</div>
            <div class="todo-meta">`;

    if (dueDate) {
      const dueDateStr = dueDate.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        year: dueDate.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
      });
      html += `📅 ${dueDateStr} • `;
    }

    html += `<span class="priority-badge ${priorityClass}">${todo.priority || 'medium'}</span>`;

    if (todo.recurrenceType && todo.recurrenceType !== 'none') {
      html += ` • 🔄 ${todo.recurrenceType}`;
    }

    html += `</div>`;

    if (todo.description) {
      html += `<div class="todo-description">${todo.description}</div>`;
    }

    if (todo.notes) {
      html += `<div class="todo-notes"><strong>Notes:</strong> ${todo.notes}</div>`;
    }

    html += `</div>`;

    return html;
  }

  private getGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  }
}