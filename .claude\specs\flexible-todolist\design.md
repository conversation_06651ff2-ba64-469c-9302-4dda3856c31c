# Flexible Todo List Application - Technical Design

## 1. Overview

This document outlines the technical design for implementing a flexible todo list application that extends the existing SvelteKit architecture. The design focuses on leveraging existing infrastructure while adding comprehensive task management capabilities with advanced scheduling, hierarchical organization, and intelligent notifications.

### 1.1 Design Principles

- **Incremental Enhancement**: Build upon existing authentication, database, and email systems
- **Database-First Design**: Use PostgreSQL's advanced features for efficient recurring task generation
- **Mobile-First Approach**: Prioritize responsive design and touch interactions
- **Scalable Architecture**: Design for growth in users and task complexity
- **Security by Design**: Maintain existing security standards while extending functionality

## 2. Architecture

### 2.1 System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Layer  │    │  Server Layer   │    │  Database Layer │
│                 │    │                 │    │                 │
│ • SvelteKit UI  │────│ • API Routes    │────│ • PostgreSQL    │
│ • Mobile First  │    │ • Auth Middleware│    │ • Drizzle ORM   │
│ • Real-time     │    │ • Business Logic│    │ • Existing Tables│
│   Updates       │    │ • Email Service │    │ • New Todo Tables│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                       ┌─────────────────┐
                       │ Background Jobs │
                       │                 │
                       │ • Task Generator│
                       │ • Email Scheduler│
                       │ • Cleanup Jobs  │
                       └─────────────────┘
```

### 2.2 Data Flow Architecture

```
User Action → SvelteKit Route → API Endpoint → Business Logic → Database
     ↓              ↓               ↓              ↓            ↓
UI Update ← Response ← JSON Result ← Data Process ← Query Result

Background Process → Task Generator → Database → Email Service → User
```

### 2.3 Integration Points

- **Authentication**: Extends existing JWT + OAuth2 system
- **Database**: Adds new tables to existing PostgreSQL schema
- **Email**: Utilizes existing Nodemailer configuration
- **UI**: Replaces mock dashboard components with functional equivalents

## 3. Database Design

### 3.1 Extended Schema

```typescript
// New tables to add to existing schema.ts

export const todoTemplates = pgTable('todo_templates', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  title: text('title').notNull(),
  description: text('description'),
  priority: text('priority').notNull().default('normal'), // low, normal, high, urgent
  category: text('category'),
  tags: text('tags').array(),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const recurrencePatterns = pgTable('recurrence_patterns', {
  id: uuid('id').primaryKey().defaultRandom(),
  templateId: uuid('template_id').notNull().references(() => todoTemplates.id, { onDelete: 'cascade' }),
  patternType: text('pattern_type').notNull(), // monthly_day, monthly_last_days, weekday, custom_interval, one_time
  patternConfig: text('pattern_config').notNull(), // JSON configuration
  startDate: timestamp('start_date').notNull(),
  endDate: timestamp('end_date'),
  timezone: text('timezone').notNull().default('UTC'),
  createdAt: timestamp('created_at').defaultNow(),
});

export const todoInstances = pgTable('todo_instances', {
  id: uuid('id').primaryKey().defaultRandom(),
  templateId: uuid('template_id').notNull().references(() => todoTemplates.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  title: text('title').notNull(),
  description: text('description'),
  dueDate: timestamp('due_date').notNull(),
  status: text('status').notNull().default('pending'), // pending, in_progress, completed, cancelled, snoozed
  priority: text('priority').notNull().default('normal'),
  completedAt: timestamp('completed_at'),
  snoozeUntil: timestamp('snooze_until'),
  notes: text('notes'),
  attachments: text('attachments').array(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const todoSubtasks = pgTable('todo_subtasks', {
  id: uuid('id').primaryKey().defaultRandom(),
  parentInstanceId: uuid('parent_instance_id').notNull().references(() => todoInstances.id, { onDelete: 'cascade' }),
  parentSubtaskId: uuid('parent_subtask_id').references(() => todoSubtasks.id, { onDelete: 'cascade' }),
  title: text('title').notNull(),
  description: text('description'),
  status: text('status').notNull().default('pending'),
  order: integer('order').notNull().default(0),
  completedAt: timestamp('completed_at'),
  createdAt: timestamp('created_at').defaultNow(),
});

export const userNotificationSettings = pgTable('user_notification_settings', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }).unique(),
  emailEnabled: boolean('email_enabled').default(true),
  emailFrequency: text('email_frequency').notNull().default('daily'), // daily, every_2_days, weekly, custom
  emailFrequencyCustom: integer('email_frequency_custom'), // days for custom frequency
  contentWindowDays: integer('content_window_days').notNull().default(7),
  emailTime: text('email_time').notNull().default('09:00'), // HH:MM format
  timezone: text('timezone').notNull().default('UTC'),
  includeOverdue: boolean('include_overdue').default(true),
  priorityFilter: text('priority_filter').array(), // which priorities to include
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const emailNotificationLog = pgTable('email_notification_log', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  emailType: text('email_type').notNull(), // scheduled, reminder, overdue
  status: text('status').notNull(), // sent, failed, queued
  sentAt: timestamp('sent_at'),
  tasksIncluded: integer('tasks_included'),
  errorMessage: text('error_message'),
  createdAt: timestamp('created_at').defaultNow(),
});
```

### 3.2 Pattern Configuration Examples

```typescript
// Monthly specific day: "15th of every month"
{
  patternType: 'monthly_day',
  patternConfig: JSON.stringify({
    day: 15,
    months: [], // empty = all months, or specific months [1,3,6,12]
  })
}

// Monthly last X days: "last 3 days of month"
{
  patternType: 'monthly_last_days',
  patternConfig: JSON.stringify({
    dayCount: 3,
    months: []
  })
}

// Specific weekdays: "every Thursday"
{
  patternType: 'weekday',
  patternConfig: JSON.stringify({
    weekdays: [4], // 0=Sunday, 1=Monday, ..., 6=Saturday
    weekInterval: 1 // every week
  })
}

// Custom interval: "every 7 days"
{
  patternType: 'custom_interval',
  patternConfig: JSON.stringify({
    intervalDays: 7,
    anchorDate: '2024-01-01'
  })
}

// One-time task
{
  patternType: 'one_time',
  patternConfig: JSON.stringify({
    dueDate: '2024-12-25T09:00:00Z'
  })
}
```

### 3.3 Database Indexes

```sql
-- Performance indexes for common queries
CREATE INDEX idx_todo_instances_user_due ON todo_instances(user_id, due_date);
CREATE INDEX idx_todo_instances_status_due ON todo_instances(status, due_date);
CREATE INDEX idx_todo_instances_template ON todo_instances(template_id);
CREATE INDEX idx_subtasks_parent ON todo_subtasks(parent_instance_id);
CREATE INDEX idx_recurrence_pattern_type ON recurrence_patterns(pattern_type);
CREATE INDEX idx_notification_log_user_date ON email_notification_log(user_id, created_at);
```

## 4. Components and Interfaces

### 4.1 Frontend Components

```
src/lib/components/todo/
├── TodoDashboard.svelte          # Main dashboard replacement
├── TodoList.svelte               # Task list with filtering
├── TodoItem.svelte               # Individual task display
├── TodoForm.svelte               # Create/edit task form
├── RecurrencePatternPicker.svelte # Date pattern selection UI
├── SubtaskManager.svelte         # Subtask CRUD operations
├── NotificationSettings.svelte   # Email preferences
└── TodoCalendar.svelte           # Calendar view (optional)
```

### 4.2 API Routes

```
src/routes/api/todos/
├── +server.ts                    # GET /api/todos (list with filters)
├── create/+server.ts             # POST /api/todos/create
├── [id]/+server.ts              # GET/PUT/DELETE /api/todos/[id]
├── [id]/subtasks/+server.ts     # Subtask operations
├── [id]/complete/+server.ts     # POST /api/todos/[id]/complete
├── [id]/snooze/+server.ts       # POST /api/todos/[id]/snooze
├── generate/+server.ts          # POST /api/todos/generate (admin)
└── notifications/
    ├── settings/+server.ts      # GET/PUT notification settings
    └── test/+server.ts          # POST test email
```

### 4.3 Business Logic Modules

```
src/lib/server/todo/
├── operations.ts                # Database operations
├── recurrence.ts               # Pattern generation logic
├── notifications.ts            # Email notification system
├── validation.ts               # Input validation
└── utils.ts                    # Helper functions
```

## 5. Data Models and Types

### 5.1 TypeScript Interfaces

```typescript
// Core todo types
export interface TodoTemplate {
  id: string;
  userId: string;
  title: string;
  description?: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  category?: string;
  tags?: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface RecurrencePattern {
  id: string;
  templateId: string;
  patternType: 'monthly_day' | 'monthly_last_days' | 'weekday' | 'custom_interval' | 'one_time';
  patternConfig: string; // JSON
  startDate: Date;
  endDate?: Date;
  timezone: string;
  createdAt: Date;
}

export interface TodoInstance {
  id: string;
  templateId: string;
  userId: string;
  title: string;
  description?: string;
  dueDate: Date;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'snoozed';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  completedAt?: Date;
  snoozeUntil?: Date;
  notes?: string;
  attachments?: string[];
  subtasks?: TodoSubtask[];
  createdAt: Date;
  updatedAt: Date;
}

export interface TodoSubtask {
  id: string;
  parentInstanceId: string;
  parentSubtaskId?: string;
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  order: number;
  completedAt?: Date;
  createdAt: Date;
}

export interface NotificationSettings {
  id: string;
  userId: string;
  emailEnabled: boolean;
  emailFrequency: 'daily' | 'every_2_days' | 'weekly' | 'custom';
  emailFrequencyCustom?: number;
  contentWindowDays: number;
  emailTime: string;
  timezone: string;
  includeOverdue: boolean;
  priorityFilter?: string[];
  createdAt: Date;
  updatedAt: Date;
}
```

### 5.2 Pattern Configuration Types

```typescript
export interface MonthlyDayConfig {
  day: number; // 1-31
  months?: number[]; // 1-12, empty = all months
}

export interface MonthlyLastDaysConfig {
  dayCount: number; // 1-15
  months?: number[];
}

export interface WeekdayConfig {
  weekdays: number[]; // 0-6, 0=Sunday
  weekInterval: number; // every N weeks
}

export interface CustomIntervalConfig {
  intervalDays: number;
  anchorDate: string; // ISO date string
}

export interface OneTimeConfig {
  dueDate: string; // ISO date string
}

export type PatternConfig = 
  | MonthlyDayConfig 
  | MonthlyLastDaysConfig 
  | WeekdayConfig 
  | CustomIntervalConfig 
  | OneTimeConfig;
```

## 6. Error Handling

### 6.1 Client-Side Error Handling

```typescript
// Error types for consistent handling
export interface TodoError {
  code: string;
  message: string;
  field?: string;
  details?: any;
}

// Common error scenarios
export const TODO_ERRORS = {
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  PATTERN_INVALID: 'PATTERN_INVALID',
  ACCESS_DENIED: 'ACCESS_DENIED',
  TASK_NOT_FOUND: 'TASK_NOT_FOUND',
  EMAIL_FAILED: 'EMAIL_FAILED',
  RATE_LIMITED: 'RATE_LIMITED'
} as const;
```

### 6.2 Server-Side Error Handling

```typescript
// Standard error response format
export interface ApiErrorResponse {
  error: {
    code: string;
    message: string;
    field?: string;
    timestamp: string;
  };
}

// Error handling middleware
export function handleTodoError(error: any): Response {
  if (error.code === 'VALIDATION_ERROR') {
    return json({ error: { code: 'VALIDATION_FAILED', message: error.message } }, { status: 400 });
  }
  
  if (error.code === 'NOT_FOUND') {
    return json({ error: { code: 'TASK_NOT_FOUND', message: 'Task not found' } }, { status: 404 });
  }
  
  // Log unexpected errors
  console.error('Unexpected todo error:', error);
  return json({ error: { code: 'INTERNAL_ERROR', message: 'An unexpected error occurred' } }, { status: 500 });
}
```

## 7. Testing Strategy

### 7.1 Unit Testing

```typescript
// Test categories for comprehensive coverage
describe('Todo Business Logic', () => {
  describe('Recurrence Pattern Generation', () => {
    test('generates correct monthly day patterns');
    test('handles February 29th in non-leap years');
    test('generates weekday patterns correctly');
    test('handles custom intervals with various anchor dates');
  });

  describe('Task Operations', () => {
    test('creates tasks with proper validation');
    test('updates task status and completion');
    test('manages subtask hierarchies');
    test('handles task snoozing and rescheduling');
  });

  describe('Notification System', () => {
    test('calculates correct notification schedules');
    test('filters tasks by user preferences');
    test('generates proper email content');
    test('handles timezone conversions');
  });
});
```

### 7.2 Integration Testing

```typescript
// API endpoint testing
describe('Todo API Integration', () => {
  test('CRUD operations with authentication');
  test('Pattern creation and instance generation');
  test('Email notification triggering');
  test('Dashboard data aggregation');
});

// Database testing
describe('Database Integration', () => {
  test('schema migrations run correctly');
  test('referential integrity maintained');
  test('query performance meets requirements');
  test('concurrent user operations');
});
```

### 7.3 End-to-End Testing

```typescript
// User workflow testing
describe('Todo User Workflows', () => {
  test('create recurring task and see instances');
  test('complete tasks and subtasks');
  test('configure and receive email notifications');
  test('mobile interface functionality');
});
```

## 8. Performance Considerations

### 8.1 Database Optimization

- **Task Instance Generation**: Use background jobs to pre-generate instances
- **Query Optimization**: Implement proper indexing for user-based queries
- **Data Archival**: Archive completed tasks older than 1 year
- **Connection Pooling**: Leverage existing Drizzle connection management

### 8.2 Frontend Performance

- **Lazy Loading**: Load task details on demand
- **Virtual Scrolling**: Handle large task lists efficiently
- **Caching**: Cache recurring pattern calculations
- **Progressive Enhancement**: Core functionality works without JavaScript

### 8.3 Email Performance

- **Batch Processing**: Group notifications by user and frequency
- **Template Caching**: Pre-compile email templates
- **Rate Limiting**: Respect existing email limits
- **Async Processing**: Use background jobs for email generation

## 9. Security Implementation

### 9.1 Data Access Control

```typescript
// Row-level security for todos
export async function getUserTodos(userId: string, filters?: TodoFilters) {
  return db.select()
    .from(todoInstances)
    .where(eq(todoInstances.userId, userId))
    // Additional filtering logic
}

// Middleware for todo access
export function requireTodoAccess(handler: RequestHandler): RequestHandler {
  return async (event) => {
    const user = event.locals.user;
    if (!user) {
      throw error(401, 'Authentication required');
    }
    
    const todoId = event.params.id;
    if (todoId) {
      const todo = await getTodoById(todoId);
      if (!todo || todo.userId !== user.id) {
        throw error(403, 'Access denied');
      }
    }
    
    return handler(event);
  };
}
```

### 9.2 Input Validation

```typescript
// Zod schemas for validation
export const TodoTemplateSchema = z.object({
  title: z.string().min(3).max(200),
  description: z.string().max(1000).optional(),
  priority: z.enum(['low', 'normal', 'high', 'urgent']),
  category: z.string().max(50).optional(),
  tags: z.array(z.string().max(30)).max(10).optional(),
});

export const RecurrencePatternSchema = z.object({
  patternType: z.enum(['monthly_day', 'monthly_last_days', 'weekday', 'custom_interval', 'one_time']),
  patternConfig: z.string().refine(isValidPatternConfig),
  startDate: z.string().datetime(),
  endDate: z.string().datetime().optional(),
  timezone: z.string(),
});
```

## 10. Monitoring and Observability

### 10.1 Metrics to Track

- Task creation and completion rates
- Email notification delivery success
- API response times
- Database query performance
- User engagement patterns

### 10.2 Logging Strategy

```typescript
// Structured logging for todo operations
export function logTodoOperation(operation: string, userId: string, details: any) {
  console.log(JSON.stringify({
    timestamp: new Date().toISOString(),
    operation,
    userId,
    details,
    service: 'todo'
  }));
}

// Error tracking
export function logTodoError(error: Error, context: any) {
  console.error(JSON.stringify({
    timestamp: new Date().toISOString(),
    error: error.message,
    stack: error.stack,
    context,
    service: 'todo'
  }));
}
```

## 11. Deployment and Migration

### 11.1 Database Migration Strategy

```sql
-- Migration files for incremental deployment
-- 001_create_todo_tables.sql
-- 002_add_indexes.sql
-- 003_add_notification_settings.sql
```

### 11.2 Feature Rollout Plan

1. **Phase 1**: Database schema and basic CRUD operations
2. **Phase 2**: Recurrence pattern system and instance generation
3. **Phase 3**: Dashboard replacement with real data
4. **Phase 4**: Email notification system
5. **Phase 5**: Advanced features and optimizations

### 11.3 Backward Compatibility

- Maintain existing mock data during development
- Provide data migration scripts for existing users
- Ensure existing authentication and session management unchanged
- Preserve existing UI components and styling