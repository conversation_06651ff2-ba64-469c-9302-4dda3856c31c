import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getTodoListsByUserId, createTodoList } from '$lib/server/db/operations.js';

export const GET: RequestHandler = async ({ locals }) => {
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const todos = await getTodoListsByUserId(locals.user.id);
    return json({ todos });
  } catch (error) {
    console.error('Failed to fetch todos:', error);
    return json({ error: 'Failed to fetch todos' }, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ request, locals }) => {
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { title, description, notes, priority, dueDate, recurrenceType, recurrencePattern } = body;

    if (!title) {
      return json({ error: 'Title is required' }, { status: 400 });
    }

    const todoData = {
      userId: locals.user.id,
      title,
      description,
      notes,
      priority: priority || 'medium',
      dueDate: dueDate ? new Date(dueDate) : null,
      recurrenceType: recurrenceType || 'none',
      recurrencePattern: recurrencePattern ? JSON.stringify(recurrencePattern) : null,
      nextDueDate: dueDate ? new Date(dueDate) : null
    };

    const todo = await createTodoList(todoData);
    return json({ todo }, { status: 201 });
  } catch (error) {
    console.error('Failed to create todo:', error);
    return json({ error: 'Failed to create todo' }, { status: 500 });
  }
};