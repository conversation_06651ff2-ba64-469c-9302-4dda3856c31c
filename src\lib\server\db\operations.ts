import { eq, and, gte, lt, desc, asc } from 'drizzle-orm';
import { db } from './index.js';
import { users, sessions, otpRequests, emailLimits, officeFlowLinks, todoLists, todoItems, emailSettings, emailHistory } from './schema.js';
import type { User, NewUser, Session, NewSession, OtpRequest, NewOtpRequest, EmailLimit, NewEmailLimit, OfficeFlowLink, NewOfficeFlowLink, TodoList, NewTodoList, TodoItem, NewTodoItem, EmailSettings, NewEmailSettings, EmailHistory, NewEmailHistory } from './schema.js';
import { v4 as uuidv4 } from 'uuid';

// User operations
export async function createUser(userData: Omit<NewUser, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
  const [user] = await db.insert(users).values(userData).returning();
  return user;
}

export async function getUserByEmail(email: string): Promise<User | null> {
  const [user] = await db.select().from(users).where(eq(users.email, email));
  return user || null;
}

export async function getUserById(id: string): Promise<User | null> {
  const [user] = await db.select().from(users).where(eq(users.id, id));
  return user || null;
}

export async function updateUserVerification(id: string, isVerified: boolean): Promise<void> {
  await db.update(users).set({ isVerified, updatedAt: new Date() }).where(eq(users.id, id));
}

export async function updateUserPassword(id: string, hashedPassword: string): Promise<void> {
  await db.update(users).set({ password: hashedPassword, updatedAt: new Date() }).where(eq(users.id, id));
}

// Session operations
export async function createSession(sessionData: Omit<NewSession, 'id' | 'createdAt'>): Promise<Session> {
  const [session] = await db.insert(sessions).values(sessionData).returning();
  return session;
}

export async function getSessionByToken(token: string): Promise<Session | null> {
  const [session] = await db.select().from(sessions).where(eq(sessions.token, token));
  return session || null;
}

export async function deleteSession(token: string): Promise<void> {
  await db.delete(sessions).where(eq(sessions.token, token));
}

export async function deleteExpiredSessions(): Promise<void> {
  await db.delete(sessions).where(lt(sessions.expiresAt, new Date()));
}

// OTP operations
export async function createOTPRequest(otpData: Omit<NewOtpRequest, 'id' | 'createdAt'>): Promise<OtpRequest> {
  const [otp] = await db.insert(otpRequests).values(otpData).returning();
  return otp;
}

export async function getOTPByRequestId(requestId: string): Promise<OtpRequest | null> {
  const [otp] = await db.select().from(otpRequests).where(
    and(
      eq(otpRequests.requestId, requestId),
      eq(otpRequests.isUsed, false),
      gte(otpRequests.expiresAt, new Date())
    )
  );
  return otp || null;
}

export async function incrementOTPAttempts(requestId: string): Promise<void> {
  await db.update(otpRequests)
    .set({ attempts: db.select({ attempts: otpRequests.attempts }).from(otpRequests).where(eq(otpRequests.requestId, requestId)) + 1 })
    .where(eq(otpRequests.requestId, requestId));
}

export async function markOTPAsUsed(requestId: string): Promise<void> {
  await db.update(otpRequests).set({ isUsed: true }).where(eq(otpRequests.requestId, requestId));
}

export async function getLatestOTPForEmail(email: string): Promise<OtpRequest | null> {
  const [otp] = await db.select().from(otpRequests)
    .where(eq(otpRequests.email, email))
    .orderBy(otpRequests.createdAt)
    .limit(1);
  return otp || null;
}

// Alias for consistency with forgot password API
export async function createOtpRequest(otpData: Omit<NewOtpRequest, 'id' | 'createdAt'>): Promise<OtpRequest> {
  return createOTPRequest(otpData);
}

export async function getOtpRequestByRequestId(requestId: string): Promise<OtpRequest | null> {
  return getOTPByRequestId(requestId);
}

export async function deleteOtpRequest(requestId: string): Promise<void> {
  await db.delete(otpRequests).where(eq(otpRequests.requestId, requestId));
}

// Email limit operations
export async function getEmailLimit(email: string): Promise<EmailLimit | null> {
  const [limit] = await db.select().from(emailLimits).where(eq(emailLimits.email, email));
  return limit || null;
}

export async function createOrUpdateEmailLimit(email: string): Promise<EmailLimit> {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const existing = await getEmailLimit(email);

  if (existing) {
    const lastRequestDate = new Date(existing.lastRequestDate);
    lastRequestDate.setHours(0, 0, 0, 0);

    if (lastRequestDate.getTime() === today.getTime()) {
      // Same day, increment count
      const [updated] = await db.update(emailLimits)
        .set({
          requestCount: existing.requestCount + 1,
          lastRequestDate: new Date()
        })
        .where(eq(emailLimits.email, email))
        .returning();
      return updated;
    } else {
      // New day, reset count
      const [updated] = await db.update(emailLimits)
        .set({
          requestCount: 1,
          lastRequestDate: new Date()
        })
        .where(eq(emailLimits.email, email))
        .returning();
      return updated;
    }
  } else {
    // Create new record
    const [created] = await db.insert(emailLimits)
      .values({
        email,
        requestCount: 1,
        lastRequestDate: new Date()
      })
      .returning();
    return created;
  }
}

export function generateRequestId(): string {
  return uuidv4();
}

// Email limit aliases for consistency
export async function getEmailLimitByEmail(email: string): Promise<EmailLimit | null> {
  return getEmailLimit(email);
}

export async function createEmailLimit(email: string, dailyCount: number, lastEmailDate: Date): Promise<EmailLimit> {
  const [created] = await db.insert(emailLimits)
    .values({
      email,
      requestCount: dailyCount,
      lastRequestDate: lastEmailDate
    })
    .returning();
  return created;
}

export async function updateEmailLimit(email: string, dailyCount: number, lastEmailDate: Date): Promise<EmailLimit> {
  const [updated] = await db.update(emailLimits)
    .set({
      requestCount: dailyCount,
      lastRequestDate: lastEmailDate
    })
    .where(eq(emailLimits.email, email))
    .returning();
  return updated;
}

// Office Flow Link operations
export async function createOfficeFlowLink(linkData: Omit<NewOfficeFlowLink, 'id' | 'createdAt' | 'updatedAt'>): Promise<OfficeFlowLink> {
  const [link] = await db.insert(officeFlowLinks).values({
    ...linkData,
    updatedAt: new Date()
  }).returning();
  return link;
}

export async function getOfficeFlowLinkByUserId(userId: string): Promise<OfficeFlowLink | null> {
  const [link] = await db.select().from(officeFlowLinks)
    .where(and(eq(officeFlowLinks.userId, userId), eq(officeFlowLinks.isActive, true)));
  return link || null;
}

export async function getOfficeFlowLinkByOfficeFlowUserId(officeFlowUserId: string): Promise<OfficeFlowLink | null> {
  const [link] = await db.select().from(officeFlowLinks)
    .where(and(eq(officeFlowLinks.officeFlowUserId, officeFlowUserId), eq(officeFlowLinks.isActive, true)));
  return link || null;
}

export async function getUserByOfficeFlowUserId(officeFlowUserId: string): Promise<User | null> {
  const result = await db.select({ user: users })
    .from(officeFlowLinks)
    .innerJoin(users, eq(officeFlowLinks.userId, users.id))
    .where(and(eq(officeFlowLinks.officeFlowUserId, officeFlowUserId), eq(officeFlowLinks.isActive, true)));

  return result[0]?.user || null;
}

export async function updateOfficeFlowLinkTokens(
  userId: string,
  accessToken: string,
  refreshToken?: string,
  expiresAt?: Date
): Promise<void> {
  await db.update(officeFlowLinks)
    .set({
      accessToken,
      refreshToken,
      tokenExpiresAt: expiresAt,
      updatedAt: new Date()
    })
    .where(and(eq(officeFlowLinks.userId, userId), eq(officeFlowLinks.isActive, true)));
}

export async function deactivateOfficeFlowLink(userId: string): Promise<void> {
  await db.update(officeFlowLinks)
    .set({
      isActive: false,
      updatedAt: new Date()
    })
    .where(eq(officeFlowLinks.userId, userId));
}

// TodoList operations
export async function createTodoList(todoData: Omit<NewTodoList, 'id' | 'createdAt' | 'updatedAt'>): Promise<TodoList> {
  const [todo] = await db.insert(todoLists).values({
    ...todoData,
    updatedAt: new Date()
  }).returning();
  return todo;
}

export async function getTodoListsByUserId(userId: string): Promise<TodoList[]> {
  return await db.select()
    .from(todoLists)
    .where(eq(todoLists.userId, userId))
    .orderBy(desc(todoLists.createdAt));
}

export async function getTodoListById(id: string, userId: string): Promise<TodoList | null> {
  const [todo] = await db.select()
    .from(todoLists)
    .where(and(eq(todoLists.id, id), eq(todoLists.userId, userId)));
  return todo || null;
}

export async function updateTodoList(id: string, userId: string, updateData: Partial<Omit<TodoList, 'id' | 'userId' | 'createdAt' | 'updatedAt'>>): Promise<TodoList | null> {
  const [updated] = await db.update(todoLists)
    .set({ ...updateData, updatedAt: new Date() })
    .where(and(eq(todoLists.id, id), eq(todoLists.userId, userId)))
    .returning();
  return updated || null;
}

export async function deleteTodoList(id: string, userId: string): Promise<void> {
  await db.delete(todoLists).where(and(eq(todoLists.id, id), eq(todoLists.userId, userId)));
}

export async function markTodoListCompleted(id: string, userId: string, isCompleted: boolean): Promise<TodoList | null> {
  const [updated] = await db.update(todoLists)
    .set({ 
      isCompleted, 
      completedAt: isCompleted ? new Date() : null,
      updatedAt: new Date() 
    })
    .where(and(eq(todoLists.id, id), eq(todoLists.userId, userId)))
    .returning();
  return updated || null;
}

// Get todos with upcoming due dates for email notifications
export async function getTodosForEmail(userId: string, startDate: Date, endDate: Date): Promise<TodoList[]> {
  return await db.select()
    .from(todoLists)
    .where(and(
      eq(todoLists.userId, userId),
      gte(todoLists.dueDate, startDate),
      lt(todoLists.dueDate, endDate)
    ))
    .orderBy(asc(todoLists.dueDate));
}

export async function getOverdueTodos(userId: string): Promise<TodoList[]> {
  const now = new Date();
  return await db.select()
    .from(todoLists)
    .where(and(
      eq(todoLists.userId, userId),
      eq(todoLists.isCompleted, false),
      lt(todoLists.dueDate, now)
    ))
    .orderBy(asc(todoLists.dueDate));
}

// TodoItem operations
export async function createTodoItem(itemData: Omit<NewTodoItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<TodoItem> {
  const [item] = await db.insert(todoItems).values({
    ...itemData,
    updatedAt: new Date()
  }).returning();
  return item;
}

export async function getTodoItemsByListId(todoListId: string): Promise<TodoItem[]> {
  return await db.select()
    .from(todoItems)
    .where(eq(todoItems.todoListId, todoListId))
    .orderBy(asc(todoItems.orderIndex), asc(todoItems.createdAt));
}

export async function updateTodoItem(id: string, updateData: Partial<Omit<TodoItem, 'id' | 'todoListId' | 'createdAt' | 'updatedAt'>>): Promise<TodoItem | null> {
  const [updated] = await db.update(todoItems)
    .set({ ...updateData, updatedAt: new Date() })
    .where(eq(todoItems.id, id))
    .returning();
  return updated || null;
}

export async function deleteTodoItem(id: string): Promise<void> {
  await db.delete(todoItems).where(eq(todoItems.id, id));
}

export async function markTodoItemCompleted(id: string, isCompleted: boolean): Promise<TodoItem | null> {
  const [updated] = await db.update(todoItems)
    .set({ 
      isCompleted, 
      completedAt: isCompleted ? new Date() : null,
      updatedAt: new Date() 
    })
    .where(eq(todoItems.id, id))
    .returning();
  return updated || null;
}

// Get todo list with its items
export async function getTodoListWithItems(id: string, userId: string): Promise<{ todoList: TodoList; items: TodoItem[] } | null> {
  const todoList = await getTodoListById(id, userId);
  if (!todoList) return null;
  
  const items = await getTodoItemsByListId(id);
  return { todoList, items };
}

// EmailSettings operations
export async function createEmailSettings(settingsData: Omit<NewEmailSettings, 'id' | 'createdAt' | 'updatedAt'>): Promise<EmailSettings> {
  const [settings] = await db.insert(emailSettings).values({
    ...settingsData,
    updatedAt: new Date()
  }).returning();
  return settings;
}

export async function getEmailSettingsByUserId(userId: string): Promise<EmailSettings | null> {
  const [settings] = await db.select()
    .from(emailSettings)
    .where(eq(emailSettings.userId, userId));
  return settings || null;
}

export async function updateEmailSettings(userId: string, updateData: Partial<Omit<EmailSettings, 'id' | 'userId' | 'createdAt' | 'updatedAt'>>): Promise<EmailSettings | null> {
  const [updated] = await db.update(emailSettings)
    .set({ ...updateData, updatedAt: new Date() })
    .where(eq(emailSettings.userId, userId))
    .returning();
  return updated || null;
}

export async function updateEmailLastSent(userId: string, lastSentAt: Date, nextSendAt: Date): Promise<void> {
  await db.update(emailSettings)
    .set({ lastSentAt, nextSendAt, updatedAt: new Date() })
    .where(eq(emailSettings.userId, userId));
}

// EmailHistory operations
export async function createEmailHistory(historyData: Omit<NewEmailHistory, 'id' | 'sentAt'>): Promise<EmailHistory> {
  const [history] = await db.insert(emailHistory).values(historyData).returning();
  return history;
}

export async function getEmailHistoryByUserId(userId: string, limit: number = 50): Promise<EmailHistory[]> {
  return await db.select()
    .from(emailHistory)
    .where(eq(emailHistory.userId, userId))
    .orderBy(desc(emailHistory.sentAt))
    .limit(limit);
}
